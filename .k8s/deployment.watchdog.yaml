apiVersion: apps/v1
kind: Deployment
metadata:
  name: watchdog-service
  labels:
    app: watchdog-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: watchdog-service
  template:
    metadata:
      labels:
        app: watchdog-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: watchdog-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/watchdog-service:3277b5b32Z250616T122748
          command: ["./watchdog"]
          args:
            [
              "svc",
              "--db-config",
              "ad_db",
              "--service-config",
              "ad_watchdog_service",
              "--ad_secrets",
              "ad_secrets",
              "--sqs-config",
              "ad_sqs",
              "--ad_website",
              "ad_website",
              "--ad_email",
              "ad_email",
              "--ad-s3",
              "ad_s3",
              "--ad_fedex",
              "ad_fedex",
            ]
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: app-environment
