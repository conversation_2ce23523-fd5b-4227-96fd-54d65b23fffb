apiVersion: apps/v1
kind: Deployment
metadata:
  name: package-service
  labels:
    app: package-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: package-service
  template:
    metadata:
      labels:
        app: package-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: package-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/package-service:c0fe55b5aZ250613T151713
          command: ["./packages"]
          args:
            [
              "svc",
              "--db-config",
              "ad_db",
              "--s3-bucket-config",
              "ad_s3",
              "--sqs-config",
              "ad_sqs",
              "--conf-file",
              "conf/packages.yaml",
              "--localize-file",
              "conf/localize.yaml",
              "--client-secret",
              "aaa",
              "--internal-auth-service-host",
              "bbbb",
              "--service-config",
              "ad_package_service",
              "--account-service-config",
              "ad_user_account_service",
              "--ad_secrets",
              "ad_secrets",
              "--ad_api_token",
              "ad_api_token",
              "--ad_es",
              "ad_es",
              "--ad_onepay",
              "ad_onepay",
              "--ad_zellepay",
              "ad_zellepay",
              "--ad_packer_service",
              "ad_packer_service",
              "--ad_email",
              "ad_email",
              "--ad_endpoint",
              "ad_endpoint",
              "--bypass-auth",
              "true",
            ]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: package-service
  labels:
    app: package-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: package-service
