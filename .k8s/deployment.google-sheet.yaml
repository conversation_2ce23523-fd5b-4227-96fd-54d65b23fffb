apiVersion: apps/v1
kind: Deployment
metadata:
  name: google-sheet-service
  labels:
    app: google-sheet-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: google-sheet-service
  template:
    metadata:
      labels:
        app: google-sheet-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: google-sheet-service
          image: 853431205376.dkr.ecr.us-west-2.amazonaws.com/google-sheet-service:473e96801Z250610T211333
          command: ["node"]
          args: ["app.js"]
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: app-environment
---
apiVersion: v1
kind: Service
metadata:
  name: google-sheet-service
  labels:
    app: google-sheet-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: google-sheet-service
