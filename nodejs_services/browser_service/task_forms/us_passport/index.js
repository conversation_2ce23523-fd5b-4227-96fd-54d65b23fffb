const fs = require('fs');
const axios = require('axios');
const moment = require('moment');
const puppeteer = require('puppeteer');
const { PuppeteerScreenRecorder } = require('puppeteer-screen-recorder');
const iso = require('iso-3166-1');
const qs = require('qs');
const us = require('us');
const AWS = require('aws-sdk');
const { PDFDocument } = require('pdf-lib');
const { STATE } = require('./data_mapping');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
const os = require('os');
const path = require('path');
const { get_order_detail, get_order_task, s3_upload_buff, create_zip, get_order_task_pod_data, sleep } = require('../../shared/helpers');
const convert_state = (state) => STATE[state] ? STATE[state] : state

let DEBUG = false
const getRecorderName = () => `Recorder_${moment().unix()}.mp4`
const us_passport_form = async (order, task, data) => {
    const document_type = { book_card: 'book_card', book: 'book', card: 'card' }[order.service.tag] || data.passport_issue_new_passport_options_document_type
    const service_tasks = order.service.tasks


    const browser = await puppeteer.launch({ headless: DEBUG ? false : "new", args: ['--no-sandbox', '--disable-setuid-sandbox'] });
    const page = await browser.newPage();
    const recorder = new PuppeteerScreenRecorder(page);
    const recorderName = getRecorderName();
    await recorder.start(recorderName);

    !DEBUG && await page.setViewport({ width: 1000, height: 1500 });
    page.setDefaultTimeout(30000);

    let i = 1;

    fs.existsSync('images') && fs.rmSync('images', { recursive: true })
    fs.mkdirSync('images', { recursive: true })

    await page.goto('https://pptform.state.gov/PassportWizardMain.aspx');

    try {

        await page.waitForSelector('#PassportWizard_portalStep_ApplyButton')
        await page.click('#PassportWizard_portalStep_ApplyButton')

        // Page 1
        await page.waitForSelector('#PassportWizard_aboutYouStep_firstNameTextBox')

        await page.type('#PassportWizard_aboutYouStep_firstNameTextBox', data.additional_question_correct_data_printed_first_name ?? data.passport_core_info_given_name)
        if (data.passport_core_info_middle_name || data.additional_question_correct_data_printed_middle_name)
            await page.type('#PassportWizard_aboutYouStep_middleNameTextBox', data.additional_question_correct_data_printed_middle_name ?? data.passport_core_info_middle_name)
        await page.type('#PassportWizard_aboutYouStep_lastNameTextBox', data.additional_question_correct_data_printed_last_name ?? data.passport_core_info_surname)

        await page.type('#PassportWizard_aboutYouStep_dobTextBox', moment(data.additional_question_correct_data_printed_date_of_birth ?? data.passport_core_info_date_of_birth).format("MMDDYYYY"))
        await page.type('#PassportWizard_aboutYouStep_pobCityTextBox', data.passport_core_info_city_of_birth)

        await page.$eval('#PassportWizard_aboutYouStep_pobCountryList', (input, value) => { input.value = value }, data.additional_question_correct_data_printed_country_of_birth ?? data.passport_core_info_country_of_birth)
        await page.$eval('#PassportWizard_aboutYouStep_pobStateList', (input, value) => { input.value = value }, convert_state(data.additional_question_correct_data_printed_state_of_birth ?? data.passport_core_info_state_of_birth))

        await page.type('#PassportWizard_aboutYouStep_ssnTextBox', data.personal_core_info_social_security_number)

        await page.$eval('#PassportWizard_aboutYouStep_sexList', (input, value) => { input.value = value }, {
            'M': 'Male',
            'F': 'Female',
        }[data.additional_question_correct_data_printed_gender ?? data.passport_core_info_gender] ?? 'X')
        if (data.additional_question_correct_data_printed_gender) {
            await page.click('#PassportWizard_aboutYouStep_SexChanging')
        }

        await page.select('#PassportWizard_aboutYouStep_heightFootList', data.personal_core_info_height.match(/(\d+)ft\.(\d+)in\./)[1])
        await page.select('#PassportWizard_aboutYouStep_heightInchList', data.personal_core_info_height.match(/(\d+)ft\.(\d+)in\./)[2])

        if (!['BLACK', 'BLONDE', 'BROWN', 'RED', 'GRAY', 'BALD'].includes(data.personal_core_info_hair_color.toUpperCase())) {
            data.personal_core_info_hair_color = 'OTHER'
        }
        if (!['AMBER', 'BLACK', 'BLUE', 'BROWN', 'GRAY', 'GREEN', 'HAZEL'].includes(data.personal_core_info_eye_color.toUpperCase())) {
            data.personal_core_info_eye_color = 'BLACK'
        }

        await page.$eval('#PassportWizard_aboutYouStep_hairList', (input, value) => { input.value = value }, data.personal_core_info_hair_color.toUpperCase())
        await page.$eval('#PassportWizard_aboutYouStep_eyeList', (input, value) => { input.value = value }, data.personal_core_info_eye_color.toUpperCase())

        // Check if age is <= 5 years old based on date of birth
        const dob = moment(data.passport_core_info_date_of_birth);
        const age = moment().diff(dob, 'years');
        if (age <= 5) {
            await page.type('#PassportWizard_aboutYouStep_occupationTextBox', 'CHILD');
        } else {
            await page.type('#PassportWizard_aboutYouStep_occupationTextBox', data.personal_occupation_occupation);
        }

        await sleep(2000)

        await page.waitForSelector('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')
        await page.click('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')

        // Page 2
        await page.waitForSelector('#PassportWizard_addressStep_mailStreetTextBox')
        await page.type('#PassportWizard_addressStep_mailStreetTextBox', data.personal_mailing_address_address)
        await page.type('#PassportWizard_addressStep_mailCityTextBox', data.personal_mailing_address_city)
        await page.$eval('#PassportWizard_addressStep_mailCountryList', (input, value) => { input.value = value }, data.personal_mailing_address_country)
        await page.$eval('#PassportWizard_addressStep_mailStateList', (input, value) => { input.value = value }, data.personal_mailing_address_state)
        await page.type('#PassportWizard_addressStep_mailZipTextBox', data.personal_mailing_address_zip_code.slice(0, 5))

        if (data.personal_permanent_address_address) {
            await page.click('#PassportWizard_addressStep_permanentAddressList_1')
            await page.waitForSelector('#PassportWizard_addressStep_permanentStreetTextBox')
            await page.type('#PassportWizard_addressStep_permanentStreetTextBox', data.personal_permanent_address_address)
            await page.type('#PassportWizard_addressStep_permanentCityTextBox', data.personal_permanent_address_city)
            await page.$eval('#PassportWizard_addressStep_permanentCountryList', (input, value) => { input.value = value }, data.personal_permanent_address_country)
            await page.$eval('#PassportWizard_addressStep_permanentStateList', (input, value) => { input.value = value }, data.personal_permanent_address_state)
            await page.type('#PassportWizard_addressStep_permanentZipTextBox', data.personal_permanent_address_zip_code.slice(0, 5))
        } else {
            await page.click('#PassportWizard_addressStep_permanentAddressList_0')
        }


        // Preferred Method of Communication
        await page.waitForNetworkIdle({ idleTime: 500 })
        await page.click('#PassportWizard_addressStep_CommunicateEmail')
        await page.waitForNetworkIdle({ idleTime: 1000 })
        await page.waitForSelector('#PassportWizard_addressStep_emailTextBox')

        let email = process.env.ad_env === 'prod' ? '<EMAIL>' : '<EMAIL>'
        if (order.service.attributes.processing_time === '1d' || data.personal_core_info_email_address) {
            email = data.personal_core_info_email_address
        }
        // Assume it is form filler 
        if (data.passport_issue_new_passport_options_processing_method) {
            email = data.personal_core_info_email_address?.toUpperCase() ?? email
        }
        await page.type('#PassportWizard_addressStep_emailTextBox', email, { delay: 10 })
        await page.type('#PassportWizard_addressStep_confirmEmailTextBox', email, { delay: 10 })


        if (data.personal_core_info_phone?.phone) {
            await page.waitForSelector('#PassportWizard_addressStep_PhoneNumberType_2')
            await page.click('#PassportWizard_addressStep_PhoneNumberType_2')
            await page.type('#PassportWizard_addressStep_addPhoneNumberTextBox', data.personal_core_info_phone?.phone.replace('+1', ''))
        }

        await sleep(2000)
        await page.click('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')



        // Travel Plans
        await page.waitForSelector('#PassportWizard_travelPlans_TripDateTextBox')
        if (data.travel_travel_plan_departure_date) {
            await page.type('#PassportWizard_travelPlans_TripDateTextBox', moment(data.travel_travel_plan_departure_date).format("MMDDYYYY"))
            await page.type('#PassportWizard_travelPlans_TripDateReturnTextBox', moment(data.travel_travel_plan_return_date).format("MMDDYYYY"))
            await page.type('#PassportWizard_travelPlans_CountriesTextBox', iso.whereAlpha3(data.travel_travel_plan_countries).country)
        }
        await page.click('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')


        // Who should we contact in case of an emergency?
        await page.waitForSelector('#PassportWizard_emergencyContacts_ecNameTextBox')
        if (data.personal_emergency_contact_given_name && data.personal_emergency_contact_surname) {
            await page.type('#PassportWizard_emergencyContacts_ecNameTextBox', data.personal_emergency_contact_given_name + ' ' + data.personal_emergency_contact_surname)
            await page.type('#PassportWizard_emergencyContacts_ecAddressTextBox', data.personal_emergency_contact_address)
            await page.type('#PassportWizard_emergencyContacts_ecCityTextBox', data.personal_emergency_contact_city)
            await page.$eval('#PassportWizard_emergencyContacts_ecStateList', (input, value) => { input.value = value }, data.personal_emergency_contact_state)
            await page.type('#PassportWizard_emergencyContacts_ZipCodeTextBox', data.personal_emergency_contact_zip_code.slice(0, 5))
            await page.type('#PassportWizard_emergencyContacts_ecPhoneTextBox', data.personal_emergency_contact_phone?.phone?.replace('+1', ''))
            await page.type('#PassportWizard_emergencyContacts_ecRelationshipTextBox', data.personal_emergency_contact_relationship)
        }

        await page.click('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')


        // Your Most Recent Passport
        let passport_most_recent_pp = 'none'

        if ((data.passport_most_recent_pp_pp_book_number || data.passport_most_recent_pp_pp_book_number_lost_stolen) && (!data.passport_most_recent_pp_pp_card_number && !data.passport_most_recent_pp_pp_card_number_lost_stolen)) {
            // Passport Book
            await page.waitForSelector('#PassportWizard_mostRecentPassport_CurrentHaveBook')
            await page.click('#PassportWizard_mostRecentPassport_CurrentHaveBook')
        }


        if ((!data.passport_most_recent_pp_pp_book_number && !data.passport_most_recent_pp_pp_book_number_lost_stolen) && (data.passport_most_recent_pp_pp_card_number || data.passport_most_recent_pp_pp_card_number_lost_stolen)) {
            // Passport Card
            await page.waitForSelector('#PassportWizard_mostRecentPassport_CurrentHaveCard')
            await page.click('#PassportWizard_mostRecentPassport_CurrentHaveCard')
        }

        if ((data.passport_most_recent_pp_pp_book_number || data.passport_most_recent_pp_pp_book_number_lost_stolen) && (data.passport_most_recent_pp_pp_card_number || data.passport_most_recent_pp_pp_card_number_lost_stolen)) {
            // Passport Book & Passport Card
            await page.waitForSelector('#PassportWizard_mostRecentPassport_CurrentHaveBoth')
            await page.click('#PassportWizard_mostRecentPassport_CurrentHaveBoth')

        }

        if ((!data.passport_most_recent_pp_pp_book_number && !data.passport_most_recent_pp_pp_book_number_lost_stolen) && (!data.passport_most_recent_pp_pp_card_number && !data.passport_most_recent_pp_pp_card_number_lost_stolen)) {
            // None
            await page.waitForSelector('#PassportWizard_mostRecentPassport_CurrentHaveNone')
            await page.click('#PassportWizard_mostRecentPassport_CurrentHaveNone')
        }
        // Passport Book
        if (data.passport_most_recent_pp_pp_book_number || data.passport_most_recent_pp_pp_book_number_lost_stolen) {
            if (service_tasks.includes('renew') ||
                service_tasks.includes('new') ||
                service_tasks.includes('change_name') ||
                service_tasks.includes('correct_data_print')) {
                await page.waitForSelector('#PassportWizard_mostRecentPassport_BookYes')
                await page.click('#PassportWizard_mostRecentPassport_BookYes')
            } else if (service_tasks.includes('lost_stolen')) {
                switch (data.passport_most_recent_pp_status_pp_book_lost_stolen) {
                    case "lost":
                        await page.waitForSelector('#PassportWizard_mostRecentPassport_BookLost')
                        await page.click('#PassportWizard_mostRecentPassport_BookLost')
                        break;
                    //stolen
                    default:
                        await page.waitForSelector('#PassportWizard_mostRecentPassport_BookStolen')
                        await page.click('#PassportWizard_mostRecentPassport_BookStolen')
                        break;
                }
            } else if (service_tasks.includes('damaged')) {
                await page.waitForSelector('#PassportWizard_mostRecentPassport_BookDamaged')
                await page.click('#PassportWizard_mostRecentPassport_BookDamaged')

            }
            await page.waitForNetworkIdle({ idleTime: 1000 })
            await sleep(5000)

            await page.waitForSelector('#PassportWizard_mostRecentPassport_BookIssueDate')
            await page.waitForSelector('#PassportWizard_mostRecentPassport_ExistingBookNumber')
            await page.type('#PassportWizard_mostRecentPassport_BookIssueDate', moment(data.passport_most_recent_pp_pp_book_issue_date ?? data.passport_most_recent_pp_pp_book_issue_date_lost_stolen).format("MMDDYYYY"))
            await page.type('#PassportWizard_mostRecentPassport_firstNameOnBook', data.passport_core_info_given_name)
            await page.type('#PassportWizard_mostRecentPassport_lastNameOnBook', data.passport_core_info_surname)
            await page.type('#PassportWizard_mostRecentPassport_ExistingBookNumber', data.passport_most_recent_pp_pp_book_number ?? data.passport_most_recent_pp_pp_book_number_lost_stolen)
            if (data.passport_most_recent_pp_pp_book_number_lost_stolen)
                if (data.additional_question_lost_stolen_did_file_police_report)
                    await page.click('#PassportWizard_mostRecentPassport_ReportLostBookYesRadioButton')
                else
                    await page.click('#PassportWizard_mostRecentPassport_ReportLostBookNoRadioButton')
        }

        // Passport Card
        if (data.passport_most_recent_pp_pp_card_number || data.passport_most_recent_pp_pp_card_number_lost_stolen) {
            if (service_tasks.includes('renew') ||
                service_tasks.includes('new') ||
                service_tasks.includes('change_name') ||
                service_tasks.includes('correct_data_print')) {
                await page.waitForSelector('#PassportWizard_mostRecentPassport_CardYes')
                await page.click('#PassportWizard_mostRecentPassport_CardYes')
            } else if (service_tasks.includes('lost_stolen')) {
                switch (data.passport_most_recent_pp_status_pp_card_lost_stolen) {
                    case "lost":
                        await page.waitForSelector('#PassportWizard_mostRecentPassport_CardLost')
                        await page.click('#PassportWizard_mostRecentPassport_CardLost')
                        break;
                    //stolen
                    default:
                        await page.waitForSelector('#PassportWizard_mostRecentPassport_CardStolen')
                        await page.click('#PassportWizard_mostRecentPassport_CardStolen')
                        break;
                }
            } else if (service_tasks.includes('damaged')) {
                await page.waitForSelector('#PassportWizard_mostRecentPassport_CardDamaged')
                await page.click('#PassportWizard_mostRecentPassport_CardDamaged')
            }

            await page.waitForNetworkIdle({ idleTime: 1000 })
            await page.waitForSelector('#PassportWizard_mostRecentPassport_CardIssueDate')
            await page.type('#PassportWizard_mostRecentPassport_CardIssueDate', moment(data.passport_most_recent_pp_pp_card_issue_date ?? data.passport_most_recent_pp_pp_card_issue_date_lost_stolen).format("MMDDYYYY"))
            await page.type('#PassportWizard_mostRecentPassport_firstNameOnCard', data.passport_core_info_given_name)
            await page.type('#PassportWizard_mostRecentPassport_lastNameOnCard', data.passport_core_info_surname)
            await page.waitForSelector('#PassportWizard_mostRecentPassport_ExistingCardNumber')
            await page.type('#PassportWizard_mostRecentPassport_ExistingCardNumber', data.passport_most_recent_pp_pp_card_number ?? data.passport_most_recent_pp_pp_card_number_lost_stolen)
            if (data.passport_most_recent_pp_pp_card_number_lost_stolen)
                if (data.additional_question_lost_stolen_did_file_police_report)
                    await page.click('#PassportWizard_mostRecentPassport_ReportLostCardYesRadioButton')
                else
                    await page.click('#PassportWizard_mostRecentPassport_ReportLostCardNoRadioButton')
        }

        await sleep(2000)
        await page.waitForSelector('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')
        await page.click('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')
        await page.waitForNetworkIdle({ idleTime: 1000 })

        //Lost - Stolen Report
        if (service_tasks.includes('lost_stolen') && !data.additional_question_lost_stolen_did_file_police_report) {
            await page.waitForSelector('#PassportWizard_lostStolenStep_reporterYesRadioButton')
            await page.click('#PassportWizard_lostStolenStep_reporterYesRadioButton')
            await page.waitForNetworkIdle({ idleTime: 1000 })

            await page.waitForSelector('#PassportWizard_lostStolenStep_policeReportNoRadioButton1')
            await page.click('#PassportWizard_lostStolenStep_policeReportNoRadioButton1')

            await page.waitForSelector('#PassportWizard_lostStolenStep_bookLostHowTextBox')
            await page.type('#PassportWizard_lostStolenStep_bookLostHowTextBox', data.additional_question_lost_stolen_how_pp_lost_stolen)

            await page.waitForSelector('#PassportWizard_lostStolenStep_bookLostWhereTextBox')
            await page.type('#PassportWizard_lostStolenStep_bookLostWhereTextBox', data.additional_question_lost_stolen_address_pp_lost_stolen ?? "N/A")

            await page.waitForSelector('#PassportWizard_lostStolenStep_bookLostDateTextBox')
            await page.type('#PassportWizard_lostStolenStep_bookLostDateTextBox', moment(data.additional_question_lost_stolen_date_pp_lost_stolen).format('MM/DD/YYYY'))

            if (data.additional_question_lost_stolen_have_other_pp_lost_stolen) {
                await page.waitForSelector('#PassportWizard_lostStolenStep_lostPrevYesRadioButton')
                await page.click('#PassportWizard_lostStolenStep_lostPrevYesRadioButton')
                await page.waitForNetworkIdle({ idleTime: 2000 })

                await page.waitForSelector('#PassportWizard_lostStolenStep_lostPassportCountList')
                await page.select('#PassportWizard_lostStolenStep_lostPassportCountList', data.additional_question_lost_stolen_how_many_pp_lost_stolen)
                await page.waitForNetworkIdle({ idleTime: 1000 })

                await page.waitForSelector('#PassportWizard_lostStolenStep_prevPassportLostDateTextBox1')
                await page.type('#PassportWizard_lostStolenStep_prevPassportLostDateTextBox1', moment(data.additional_question_lost_stolen_approximate_dates_lost_stolen).format('MM/DD/YYYY'))

                if (data.additional_question_lost_stolen_did_file_police_report_other_pp)
                    await page.click('#PassportWizard_lostStolenStep_policeReportYesRadioButton2')
                else
                    await page.click('#PassportWizard_lostStolenStep_policeReportNoRadioButton2')
            } else {
                await page.waitForSelector('#PassportWizard_lostStolenStep_lostPrevNoRadioButton')
                await page.click('#PassportWizard_lostStolenStep_lostPrevNoRadioButton')
            }

            await sleep(2000)
            await page.waitForSelector('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')

            await page.click('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')
            await page.waitForNetworkIdle({ idleTime: 3000 })
        }


        pageHeaderTitle = (await page.$eval('.PageHeader h1', (element) => element.textContent)).trim();

        if (pageHeaderTitle === 'Your Most Recent Passport') {

            if (service_tasks.includes('correct_data_print')
            ) {
                await page.waitForSelector('#PassportWizard_mostRecentPassportContinued_dataIncorrectBook')
                await page.click('#PassportWizard_mostRecentPassportContinued_dataIncorrectBook')
                await page.waitForSelector('#PassportWizard_mostRecentPassportContinued_IncorrectLastName')
                if (data.additional_question_correct_data_printed_last_name) {
                    await page.click('#PassportWizard_mostRecentPassportContinued_IncorrectLastName')
                }
                if (data.additional_question_correct_data_printed_first_name) {
                    await page.click('#PassportWizard_mostRecentPassportContinued_IncorrectFirstName')
                }
                if (data.additional_question_correct_data_printed_middle_name) {
                    await page.click('#PassportWizard_mostRecentPassportContinued_IncorrectMiddleName')
                }
                if (data.additional_question_correct_data_printed_country_of_birth) {
                    await page.click('#PassportWizard_mostRecentPassportContinued_IncorrectPlaceOfBirth')
                }
                //TODO Neeed to Check - Forn filler does not have any field for most recent dob in PP and correct DOB
                if (data.additional_question_correct_data_printed_date_of_birth) {
                    await page.click('#PassportWizard_mostRecentPassportContinued_IncorrectDateOfBirth')
                }
                if (data.additional_question_correct_data_printed_gender) {
                    await page.click('#PassportWizard_mostRecentPassportContinued_IncorrectSex')
                }
            } else {
                await new Promise(resolve => setTimeout(resolve, 1000))
                if (await page.$('#PassportWizard_mostRecentPassportContinued_dataIncorrectNone')) {
                    await page.click('#PassportWizard_mostRecentPassportContinued_dataIncorrectNone')
                }
            }
            //Change Name
            if (data.personal_name_change_is_your_name_different || service_tasks.includes('change_name')) {

                await page.waitForNetworkIdle({ idleTime: 500 })
                if (data.passport_most_recent_pp_pp_book_number) {
                    if ((await page.$('#PassportWizard_mostRecentPassportContinued_nameChangeBook'))) {
                        await page.click('#PassportWizard_mostRecentPassportContinued_nameChangeBook')
                        // await page.waitForNetworkIdle({ idleTime: 500 })
                    }
                    if ((await page.$('#PassportWizard_mostRecentPassportContinued_nameChangeCard'))) {
                        await page.click('#PassportWizard_mostRecentPassportContinued_nameChangeCard')
                        // await page.waitForNetworkIdle({ idleTime: 500 })
                    }
                    if ((await page.$('#PassportWizard_mostRecentPassportContinued_nameChangeBoth'))) {
                        await page.click('#PassportWizard_mostRecentPassportContinued_nameChangeBoth')
                        // await page.waitForNetworkIdle({ idleTime: 500 })
                    }
                    await sleep(2000)
                }

                await page.waitForSelector('#PassportWizard_mostRecentPassportContinued_NameChangeReason_0')
                await page.waitForSelector('#PassportWizard_mostRecentPassportContinued_NameChangeReason_1')
                if (data.personal_name_change_name_changed_by === "changed_by_marriage") {
                    await page.click('#PassportWizard_mostRecentPassportContinued_NameChangeReason_0')
                } else {
                    await page.click('#PassportWizard_mostRecentPassportContinued_NameChangeReason_1')
                }
                await page.type('#PassportWizard_mostRecentPassportContinued_NameChangeDate', moment(data.personal_name_change_date_of_name_change).format("MMDDYYYY"))
                await page.type('#PassportWizard_mostRecentPassportContinued_NameChangePlace', data.personal_name_change_place_of_name_change)
                if (data.personal_name_change_name_change_document) {
                    await page.click("#PassportWizard_mostRecentPassportContinued_NameChangeCertified_0")
                } else {
                    await page.click("#PassportWizard_mostRecentPassportContinued_NameChangeCertified_1")
                }

                await page.evaluate(() => {
                    alert = () => { }
                }) // Skip alert popup
            } else {
                try {
                    await new Promise(resolve => setTimeout(resolve, 1000))
                    if ((await page.$('#PassportWizard_mostRecentPassportContinued_nameChangeNone'))) {
                        await page.click('#PassportWizard_mostRecentPassportContinued_nameChangeNone')
                    }
                } catch (e) {
                    console.log(e.message)
                }
            }
            await sleep(2000)
            if ((await page.$('#PassportWizard_mostRecentPassportContinued_LimitedIssueBook_1'))) {
                await page.click('#PassportWizard_mostRecentPassportContinued_LimitedIssueBook_1')
            }


            await sleep(5000)
            await page.waitForSelector('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')
            await page.click('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')
            await page.waitForNetworkIdle({ idleTime: 2000 })
        }

        await sleep(5000)

        pageHeaderTitle = (await page.$eval('.PageHeader h1', (element) => element.textContent)).trim();

        if (pageHeaderTitle === `Applicant's Parent & Spouse Information`) {
            // Applicant's Parent & Spouse Information
            if (data.family_parent_info_1_given_name) {
                await page.waitForSelector('#PassportWizard_moreAboutYouStep_parent1FirstNameTextBox')
                await page.type('#PassportWizard_moreAboutYouStep_parent1FirstNameTextBox', [data.family_parent_info_1_given_name, data.family_parent_info_1_middle_name].filter(Boolean).join(" "))
                await page.type('#PassportWizard_moreAboutYouStep_parent1LastNameTextBox', data.family_parent_info_1_surname)
                await page.type('#PassportWizard_moreAboutYouStep_parent1BirthDateTextBox', moment(data.family_parent_info_1_date_of_birth).format("MMDDYYYY"))

                // Please enter the place of birth of your parent. 
                // Include City & State if in the U.S. or City & Country as it is presently known.
                if (data.family_parent_info_1_country_of_birth === 'USA') {
                    await page.type('#PassportWizard_moreAboutYouStep_parent1BirthPlaceTextBox', [data.family_parent_info_1_city_of_birth, us.lookup(data.family_parent_info_1_state_of_birth).name].filter(v => v).join(' '))
                } else {
                    await page.type('#PassportWizard_moreAboutYouStep_parent1BirthPlaceTextBox', [data.family_parent_info_1_city_of_birth, iso.whereAlpha3(data.family_parent_info_1_country_of_birth).country].filter(v => v).join(' '))
                }

                if (data.family_parent_info_1_nationality === 'USA') {
                    await page.click('#PassportWizard_moreAboutYouStep_parent1CitizenList_0')
                } else {
                    await page.click('#PassportWizard_moreAboutYouStep_parent1CitizenList_1')
                }



                data.family_parent_info_1_gender === "M" && await page.click('#PassportWizard_moreAboutYouStep_parent1SexList_0')
                data.family_parent_info_1_gender === "F" && await page.click('#PassportWizard_moreAboutYouStep_parent1SexList_1')
                data.family_parent_info_1_gender === "X" && await page.click('#PassportWizard_moreAboutYouStep_parent1SexList_2')
            } else {
                await page.waitForSelector('#PassportWizard_moreAboutYouStep_unknownParent1CheckBox')
                await page.click('#PassportWizard_moreAboutYouStep_unknownParent1CheckBox')
                await page.waitForNetworkIdle({ idleTime: 1000 })
            }

            if (data.family_parent_info_2_given_name) {
                await page.waitForSelector('#PassportWizard_moreAboutYouStep_parent2FirstNameTextBox')
                await page.type('#PassportWizard_moreAboutYouStep_parent2FirstNameTextBox', data.family_parent_info_2_given_name + ' ' + (data.family_parent_info_2_middle_name ?? ''))
                await page.type('#PassportWizard_moreAboutYouStep_parent2LastNameTextBox', data.family_parent_info_2_surname)
                await page.type('#PassportWizard_moreAboutYouStep_parent2BirthDateTextBox', moment(data.family_parent_info_2_date_of_birth).format("MMDDYYYY"))

                // Please enter the place of birth of your parent. Include City & State if in the U.S. or City & Country as it is presently known.
                if (data.family_parent_info_2_country_of_birth === 'USA') {
                    await page.type('#PassportWizard_moreAboutYouStep_parent2BirthPlaceTextBox', [data.family_parent_info_2_city_of_birth, us.lookup(data.family_parent_info_2_state_of_birth)?.name].filter(v => v).join(' '))
                } else {
                    await page.type('#PassportWizard_moreAboutYouStep_parent2BirthPlaceTextBox', [data.family_parent_info_2_city_of_birth, iso.whereAlpha3(data.family_parent_info_2_country_of_birth).country].filter(v => v).join(' '))
                }

                if (data.family_parent_info_2_nationality === 'USA') {
                    await page.click('#PassportWizard_moreAboutYouStep_parent2CitizenList_0')
                } else {
                    await page.click('#PassportWizard_moreAboutYouStep_parent2CitizenList_1')
                }

                data.family_parent_info_2_gender === "M" && await page.click('#PassportWizard_moreAboutYouStep_parent2SexList_0')
                data.family_parent_info_2_gender === "F" && await page.click('#PassportWizard_moreAboutYouStep_parent2SexList_1')
                data.family_parent_info_2_gender === "X" && await page.click('#PassportWizard_moreAboutYouStep_parent2SexList_2')
            } else {
                await page.waitForSelector('#PassportWizard_moreAboutYouStep_unknownParent2CheckBox')
                await page.click('#PassportWizard_moreAboutYouStep_unknownParent2CheckBox')
                await page.waitForNetworkIdle({ idleTime: 1000 })
            }

            await sleep(2000)

            if (data.personal_core_info_marital_status === 'single' || data.personal_core_info_marital_status == null)
                await page.click('#PassportWizard_moreAboutYouStep_marriedList_1')
            else {
                // Spouse Of Applicant
                await page.click('#PassportWizard_moreAboutYouStep_marriedList_0')
                await page.waitForSelector('#PassportWizard_moreAboutYouStep_spouseNameTextBox')
                await page.waitForSelector('#PassportWizard_moreAboutYouStep_spouseLastNameTextBox')

                await page.type('#PassportWizard_moreAboutYouStep_spouseNameTextBox', [data.family_spouse_given_name, data.family_spouse_middle_name].filter(v => v).join(' '))
                await page.type('#PassportWizard_moreAboutYouStep_spouseLastNameTextBox', data.family_spouse_surname)
                await page.type('#PassportWizard_moreAboutYouStep_spouseBirthDateTextBox', moment(data.family_spouse_date_of_birth).format("MMDDYYYY"))

                if (data.family_spouse_country_of_birth === 'USA') {
                    await page.type('#PassportWizard_moreAboutYouStep_spouseBirthplaceTextBox', [data.family_spouse_city_of_birth, us.lookup(data.family_spouse_state_of_birth)?.name].filter(v => v).join(' '))
                } else {
                    await page.type('#PassportWizard_moreAboutYouStep_spouseBirthplaceTextBox', [data.family_spouse_city_of_birth, iso.whereAlpha3(data.family_spouse_country_of_birth).country].filter(v => v).join(' '))
                }

                if (data.family_spouse_nationality === 'USA')
                    await page.click('#PassportWizard_moreAboutYouStep_spouseCitizenList_0')
                else
                    await page.click('#PassportWizard_moreAboutYouStep_spouseCitizenList_1')


                // Has applicant ever been widowed or divorced?
                await page.type('#PassportWizard_moreAboutYouStep_marriedDateTextBox', moment(data.family_spouse_date_of_marrige ?? data.family_spouse_date_of_marriage).format("MMDDYYYY"))
                if (['widowed', 'divorced'].includes(data.personal_core_info_marital_status)) {
                    await page.click('#PassportWizard_moreAboutYouStep_divorcedList_0')
                    await page.waitForSelector('#PassportWizard_moreAboutYouStep_divorcedDateTextBox')
                    await page.type('#PassportWizard_moreAboutYouStep_divorcedDateTextBox', moment(data.family_spouse_divorce_date).format("MMDDYYYY"))
                } else {
                    await page.click('#PassportWizard_moreAboutYouStep_divorcedList_1')
                }

            }
            await page.waitForNetworkIdle({ idleTime: 1000 })
            await page.waitForSelector('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')
            await page.click('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')

        }

        // List all other names you have used
        await page.waitForNetworkIdle({ idleTime: 1000 })
        await page.waitForSelector('#PassportWizard_otherNameStep_addOtherFirstTextBox')
        if (data.passport_core_info_other_name) {
            for (const name of data.passport_core_info_other_name.split(',')) {
                await page.type('#PassportWizard_otherNameStep_addOtherFirstTextBox', name.split(" ").slice(0, -1).join(" "))
                await page.type('#PassportWizard_otherNameStep_addOtherLastTextBox', name.split(" ").slice(-1)[0])
                await page.click('#PassportWizard_otherNameStep_addButton')
                await page.waitForNetworkIdle({ idleTime: 1000 })
            }
        }

        await page.waitForSelector('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')
        await page.click('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')

        // Personal Application Review
        await page.waitForNetworkIdle({ idleTime: 2000 })
        await page.waitForSelector('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')
        await page.click('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')

        // Passport Products and Fees
        // Passport Options
        if (document_type === 'book') {
            await page.waitForSelector('#PassportWizard_feesStep_bookFee')
            await page.click('#PassportWizard_feesStep_bookFee')
            // Large book
            await page.waitForSelector('#PassportWizard_feesStep_bookType52')
            await page.click('#PassportWizard_feesStep_bookType52')
        }

        if (document_type === 'card') {
            await page.waitForSelector('#PassportWizard_feesStep_cardFee')
            await page.click('#PassportWizard_feesStep_cardFee')
        }

        if (document_type === 'book_card') {
            await page.waitForSelector('#PassportWizard_feesStep_bookCardFee')
            await page.click('#PassportWizard_feesStep_bookCardFee')
            // Large book
            await page.waitForSelector('#PassportWizard_feesStep_bothBookType52')
            await page.click('#PassportWizard_feesStep_bothBookType52')
        }

        // Routine Service (FREE)
        if (data.service_core_info_processing_time === '10-12w' || data.passport_issue_new_passport_options_processing_method === "routine_service") {
            await page.waitForSelector('#PassportWizard_feesStep_routineService')
            await page.click('#PassportWizard_feesStep_routineService')
        }

        // Expedited Service ($60)
        if (data.service_core_info_processing_time === '4-6w' || data.passport_issue_new_passport_options_processing_method === "expedited_service") {
            await page.waitForSelector('#PassportWizard_feesStep_expeditedService')
            await page.click('#PassportWizard_feesStep_expeditedService')
        }

        // Expedited at Agency Service ($60)
        if (data.service_core_info_processing_time === '1d' || data.passport_issue_new_passport_options_processing_method === "expedited_agency_service") {
            await page.waitForSelector('#PassportWizard_feesStep_expeditedAgencyService')
            await page.click('#PassportWizard_feesStep_expeditedAgencyService')
        }
        await page.waitForNetworkIdle({ idleTime: 1000 })
        await page.waitForSelector('#PassportWizard_FinishNavigationTemplateContainerID_FinishButton')
        await page.click('#PassportWizard_FinishNavigationTemplateContainerID_FinishButton')
        await page.waitForNetworkIdle({ idleTime: 2000 })
        await page.evaluate(() => {
            alert = () => { }
        }) // Skip alert popup

        //DS-64 Signature
        if (service_tasks.includes('lost_stolen') && !data.additional_question_lost_stolen_did_file_police_report) {
            await page.waitForSelector('#PassportWizard_esignatureStep_lostOrStolenDelivery_1')
            await page.click('#PassportWizard_esignatureStep_lostOrStolenDelivery_1')
            await page.waitForNetworkIdle({ idleTime: 1000 })
            await page.waitForSelector('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')
            await page.click('#PassportWizard_StepNavigationTemplateContainerID_StartNextPreviousButton')
            await page.waitForNetworkIdle({ idleTime: 2000 })
        }
        //
        const formSelectors = {
            DS64: '#PassportWizard_nextStepsStep_OMB_pnlOMB_DS64',
            DS11: '#PassportWizard_nextStepsStep_OMB_pnlOMB_DS11',
            DS5504: '#PassportWizard_nextStepsStep_OMB_pnlOMB_DS5504',
            DS82: '#PassportWizard_nextStepsStep_OMB_pnlOMB_DS82',
        };
        const availableForms = [];
        for (const formName in formSelectors) {
            const elementHandle = await page.$(formSelectors[formName]);
            if (elementHandle) {
                availableForms.push(formName);
            }
        }
        // Extract form data and download form
        const formData = await page.evaluate(() => {
            const form = document.querySelector('form');
            const formData = new FormData(form);
            return Object.fromEntries(formData);
        })

        formData['PassportWizard$nextStepsStep$ConfirmationCheckBox'] = 'on'
        formData['PassportWizard$nextStepsStep$printFormButton'] = 'Print Form'
        // formData.PassportWizard$esignatureStep$lostOrStolenDelivery
        console.log("DOWNLOADING FORM.................")
        const cookies = await page.cookies();
        const resp = await axios({
            method: 'POST',
            url: 'https://pptform.state.gov/PassportWizardMain.aspx?AspxAutoDetectCookieSupport=1',
            data: qs.stringify(formData),
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://pptform.state.gov',
                'Referer': 'https://pptform.state.gov/PassportWizardMain.aspx?AspxAutoDetectCookieSupport=1',
                'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36',
                'Cookie': cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; '),
            },
            responseType: 'arraybuffer',
        })
        console.log("DOWNLOADED FORM SUCCESS.................")
        fs.writeFileSync('FORM.pdf', resp.data)
        await recorder.stop();
        await browser.close()
        return { form_data: resp.data, form_name: availableForms, recorderName: recorderName }
    } catch (error) {
        console.log(error)
        console.log(error.message)
        await recorder.stop();
        await browser.close()
        return null
    }

}

async function extract_pdf(pdfBuffer, formList) {
    const cutRules = {
        DS11: [5, 2], DS64: [1, 2],
        DS5504: [5, 2], DS82: [5, 2]
    };
    if (formList.includes('DS11') && formList.includes('DS64')) {
        cutRules.DS64 = [1, 2]
        cutRules.DS11 = [7, 2];
    }

    const tmpInput = path.join(os.tmpdir(), `in-${Date.now()}.pdf`);
    const tmpOutput = path.join(os.tmpdir(), `out-${Date.now()}.pdf`);
    const tempFiles = [];

    try {
        fs.writeFileSync(tmpInput, pdfBuffer);

        const pdfFiles = await Promise.all(formList.map(async (form, index) => {
            const [start, count] = cutRules[form];
            const tmpIntermediate = path.join(os.tmpdir(), `inter-${Date.now()}-${index}.pdf`);
            tempFiles.push(tmpIntermediate);
            await execPromise(`pdftk "${tmpInput}" cat ${start}-${start + count - 1} output "${tmpIntermediate}"`);
            return tmpIntermediate;
        }));

        const fileList = pdfFiles.join(' ');
        await execPromise(`pdftk ${fileList} cat output "${tmpOutput}"`);
        return fs.readFileSync(tmpOutput);

    } finally {
        fs.unlinkSync(tmpInput);
        tempFiles.forEach(file => fs.unlinkSync(file));
    }
}
const MAX_RETRY = 5;
const create_form = async ({ pod_data, order, task }) => {
    console.log(JSON.stringify(pod_data, null, 2))
    let form = null
    for (let i = 0; i < MAX_RETRY; i++) {
        form = await us_passport_form(order, task, pod_data)
        if (form != null) {
            break
        }
    }

    const result = {
        success: false,
        form_file: "",
        form_callback: {},
    }

    const buckets = JSON.parse(process.env.ad_s3)
    const file_name = `Application_Form_${pod_data.passport_core_info_surname}_${pod_data.passport_core_info_given_name}_US_Passport_${moment().unix()}.pdf`
    await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${RECODER_NAME}`, fs.readFileSync(RECODER_NAME))

    fs.rmSync('images', { recursive: true })
    fs.rmSync(RECODER_NAME, { recursive: true })

    result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${RECODER_NAME}`
    console.log("STEPS:", result.form_callback.steps)
    if (!form) {
        return result
    }
    const new_pdf_buff = await extract_pdf(form.form_data, form.form_name)
    fs.writeFileSync("images/form.pdf", new_pdf_buff);
    await s3_upload_buff(buckets.ariadirect_prod_applications, `tasks/${file_name}`, new_pdf_buff)
    result.form_file = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/tasks/${file_name}`
    result.success = true

    let recorderName = null;
    if (form && form.recorderName) {
        recorderName = form.recorderName;
        await s3_upload_buff(buckets.ariadirect_prod_applications, `steps/${recorderName}`, fs.readFileSync(recorderName))
        fs.rmSync(recorderName, { recursive: true })
        result.form_callback.steps = `https://${buckets.ariadirect_prod_applications}.s3.amazonaws.com/steps/${recorderName}`
    }

    fs.rmSync('images', { recursive: true })
    console.log("STEPS:", result.form_callback.steps)
    return result
}

const main = async (order_id, task_id) => {
    try {
        const { order, task, pod_data } = await get_order_task_pod_data(order_id, task_id)
        const data = await create_form({ pod_data, order, task })
        return data
    } catch (error) {
        console.log(error)
    }
}

module.exports = { main, create_form }